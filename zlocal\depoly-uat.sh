#!/bin/bash

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 打包文件
tar -zcvf ebook-api.tar.gz --exclude=node_modules --exclude=docker-entrypoint-initdb --exclude=docker-compose.yml --exclude=*.md --exclude=deploy.sh --exclude=.env --exclude=zlocal --exclude=.idea ./*

# 上传文件
pscp -i zlocal/JRC.ppk ebook-api.tar.gz ubuntu@122.248.233.120:/home/<USER>/staging/ebook-api/

# 清理本地文件
if [ -f "ebook-api.tar.gz" ]; then
   rm ebook-api.tar.gz
fi
