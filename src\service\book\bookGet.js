const Book = require("../../../Model/Book");
const Admin = require("../../../Model/Admin");
const mongoose = require('mongoose');

function regExpReplaceSpecialCharacters(str) {
    if (!str) return '';
    return str.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
}

async function filterRecords(res, match, sortbycol, sortByType, collection_type) {
    let is_deleted = [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }];
    if (match.$or) {
        let or_condition = match.$or;
        if (or_condition.length > 0) {
            or_condition.map((e, index) => {
                or_condition[index] = { ...e, $or: is_deleted }
            });
        };
    } else {
        match = {
            ...match,
            $or: is_deleted
        }
    }
    
    let _match = collection_type ? {
        $and: [
            { collection_type },
            match
        ]
    } : match;

    try {
        let books = await Book.aggregate([
            { $match: _match },
            {
                $lookup: {
                    from: 'categories',
                    localField: 'category_id',
                    foreignField: '_id',
                    as: 'category'
                }
            },
            { $unwind: "$category" },
            {
                $project: {
                    _id: 1,
                    title: 1,
                    isbn_no: 1,
                    author: 1,
                    stock_quantity: 1,
                    available_quantity: 1,
                    cover_photo: 1,
                    book_pdf: 1,
                    preview_book: 1,
                    added_by: 1,
                    added_at: 1,
                    publish_date: 1,
                    publishingGroup: 1,
                    imprints: 1,
                    category_id: "$category._id",
                    category_name: "$category.name"
                }
            },
            { $sort: { [sortbycol]: sortByType === 'asc' ? 1 : -1 } }
        ]);

        res.json({
            code: 200,
            data: books,
            message: "Operation successful."
        });
    } catch (error) {
        console.error('Error in filterRecords:', error);
        res.status(500).json({
            code: 500,
            message: "Internal server error"
        });
    }
}

async function getBooks(req) {
    const collection_type = req.header("x-current-collection");
    const { searchText, email, sortBy: sortParam, category, is_available, query, queryType } = req.body;
    
    let sortBy = sortParam || 'added_at';
    let sortByType = 'desc';
    
    if (sortParam && sortParam.includes('-')) {
        const [field, type] = sortParam.split('-');
        sortBy = field;
        sortByType = type;
    }

    // Handle email-based search (original logic from the route)
    if (email) {
        let match = {};
        if (searchText) {
            match = {
                "$and": [{
                    $or: [
                        { isbn_no: { $regex: regExpReplaceSpecialCharacters(searchText.trim()), $options: "i" } },
                        { title: { $regex: regExpReplaceSpecialCharacters(searchText.trim()), $options: "i" } },
                        { author: { $regex: regExpReplaceSpecialCharacters(searchText.trim()), $options: "i" } },
                        { excerpt: { $regex: regExpReplaceSpecialCharacters(searchText.trim()), $options: "i" } }
                    ]
                }, {
                    $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
                }]
            };
        } else {
            match = {
                $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
            };
        }

        let _match = collection_type ? {
            $and: [
                { collection_type },
                match
            ]
        } : match;

        const result = await Book.aggregate([
            { $match: _match },
            {
                $lookup: {
                    from: 'categories',
                    localField: 'category_id',
                    foreignField: '_id',
                    as: 'categories'
                }
            },
            { $unwind: "$categories" },
            {
                $group: {
                    "_id": {
                        "book_id": "$_id",
                        "title": "$title",
                        "isbn_no": "$isbn_no",
                        "author": "$author",
                        "publishingGroup": "$publishingGroup",
                        "imprints": "$imprints",
                        "available_quantity": "$available_quantity",
                        "stock_quantity": "$stock_quantity",
                        "cover_photo": "$cover_photo",
                        "preview_book": "$preview_book",
                        "added_by": "$added_by",
                        "added_at": "$added_at",
                        "publish_date": "$publish_date",
                        "book_pdf": "$book_pdf"
                    },
                    "category_id": { $addToSet: "$categories._id" },
                    "categories": { $addToSet: "$categories.name" },
                    "count": { $sum: 1 }
                }
            },
            {
                $group: {
                    "_id": null,
                    "data": {
                        "$push": {
                            "book_id": "$_id.book_id",
                            "title": "$_id.title",
                            "isbn_no": "$_id.isbn_no",
                            "author": "$_id.author",
                            "stock_quantity": "$_id.stock_quantity",
                            "available_quantity": "$_id.available_quantity",
                            "imprints": "$_id.imprints",
                            "publishingGroup": "$_id.publishingGroup",
                            "cover_photo": "$_id.cover_photo",
                            "book_pdf": "$_id.book_pdf",
                            "preview_book": "$_id.preview_book",
                            "added_by": "$_id.added_by",
                            "added_at": "$_id.added_at",
                            "publish_date": "$_id.publish_date",
                            "count": "$count",
                            "category_id": "$category_id",
                            "categories": "$categories"
                        }
                    }
                }
            },
            { $sort: { 'data.added_at': -1 } },
            { $project: { data: 1 } }
        ]);

        let sortedBooks = [];
        if (result && result.length > 0) {
            sortedBooks = [...result[0].data, ...sortedBooks];
        }

        let booksArr = [];
        if (sortedBooks.length > 0) {
            sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
            const resx = await Admin.findOne({ email: email });
            
            for (let sortedBook of sortedBooks) {
                let c = 0;
                let cat = [];
                for (let category_id of sortedBook.category_id) {
                    cat.push({ _id: category_id, name: sortedBook.categories[c] });
                    c++;
                }

                let book = {
                    _id: sortedBook.book_id,
                    category_ids: cat,
                    category_id: sortedBook.categories,
                    cover_photo: sortedBook.cover_photo,
                    book_pdf: sortedBook.book_pdf,
                    preview_book: sortedBook.preview_book,
                    title: sortedBook.title,
                    isbn_no: sortedBook.isbn_no,
                    author: sortedBook.author,
                    stock_quantity: sortedBook.stock_quantity,
                    available_quantity: sortedBook.available_quantity,
                    publishingGroup: sortedBook.publishingGroup,
                    imprints: sortedBook.imprints,
                    publish_date: sortedBook.publish_date,
                    added_by: sortedBook.added_by
                };

                if (resx.role === '3' && ((book.added_by).toString()) === ((resx._id).toString())) {
                    booksArr.push(book);
                } else if (resx && (resx.role === '1' || resx.role === '2')) {
                    booksArr.push(book);
                }
            }
            return {
                code: 200,
                data: booksArr,
                message: "Operation successful."
            };
        } else {
            return {
                code: 200,
                data: booksArr,
                message: "Operation successful."
            };
        }
    }

    // Handle other filter combinations
    if (!category && !is_available && !query) {
        return { useFilter: true, match: {}, sortBy, sortByType };
    }

    if (category && !is_available && !query) {
        return { 
            useFilter: true, 
            match: { category_id: mongoose.Types.ObjectId(category) },
            sortBy,
            sortByType
        };
    }

    if (!category && is_available && !query) {
        return { 
            useFilter: true, 
            match: { available_quantity: { $gt: 0 } },
            sortBy,
            sortByType
        };
    }

    if (!category && !is_available && query) {
        return handleQueryFilter(query, queryType, null, false, sortBy, sortByType);
    }

    if (category && !is_available && query) {
        return handleQueryFilter(query, queryType, category, false, sortBy, sortByType);
    }

    if (category && is_available && !query) {
        return {
            useFilter: true,
            match: {
                $and: [
                    { available_quantity: { $gt: 0 } },
                    { category_id: mongoose.Types.ObjectId(category) }
                ]
            },
            sortBy,
            sortByType
        };
    }

    if (category && is_available && query) {
        return handleQueryFilter(query, queryType, category, true, sortBy, sortByType);
    }

    if (!category && is_available && query) {
        return handleQueryFilter(query, queryType, null, true, sortBy, sortByType);
    }

    return { useFilter: true, match: {}, sortBy, sortByType };
}

function handleQueryFilter(query, queryType, category, isAvailable, sortBy = 'added_at', sortByType = 'desc') {
    const regex = { $regex: regExpReplaceSpecialCharacters(query), $options: "i" };
    let match = {};

    // Set up the initial match based on query type
    if (queryType === '1') {
        match.title = regex;
    } else if (queryType === '2') {
        match.author = regex;
    } else if (queryType === '3') {
        match.excerpt = regex;
    } else if (queryType === '4') {
        match = {
            $or: [
                { title: regex },
                { author: regex },
                { excerpt: regex }
            ]
        };
    }

    // Handle category filter
    if (category) {
        match = {
            $and: [
                match,
                { category_id: mongoose.Types.ObjectId(category) }
            ]
        };
    }

    // Handle availability filter
    if (isAvailable) {
        match = {
            $and: [
                match || {},
                { available_quantity: { $gt: 0 } }
            ]
        };
    }

    // Add deleted records filter
    if (match.$and) {
        match.$and.push({
            $or: [
                { is_deleted: { $exists: false } }, 
                { is_deleted: { $exists: true, $eq: false } }
            ]
        });
    } else {
        match.$or = [
            ...(match.$or || []),
            { is_deleted: { $exists: false } }, 
            { is_deleted: { $exists: true, $eq: false } }
        ];
    }

    return { 
        useFilter: true, 
        match,
        sortBy,
        sortByType
    };
}

module.exports = {
    getBooks,
    filterRecords
};
