const mongoose = require("mongoose");
require("dotenv").config();

// 连接数据库
mongoose.connect(process.env.DB_URL, { 
    useUnifiedTopology: true, 
    useNewUrlParser: true, 
    useCreateIndex: true, 
    useFindAndModify: false 
}).then((db) => {
    console.log("MongoDB connected");
    runTests();
}).catch((error) => console.log("Lost Connection : " + error));

// 导入需要的模型和函数
const Reading = require('./Model/Reading');
const ReadingRecord = require('./Model/ReadingRecord');
const Reserve = require('./Model/Reserve');
const Book = require('./Model/Book');
const User = require('./Model/User');
const Borrow = require('./Model/Borrow');
const BookHelper = require('./Helper/BookHelper');
const { batchReturnReadingBooks } = require('./Routes/Reading');
const { MAX_BORROW_DAYS } = require('./consts');

async function runTests() {
    try {
        console.log('\n=== 开始测试修复后的功能 ===\n');
        
        // 测试1: 批量归还阅读记录的修复
        await testBatchReturnReadingBooks();
        
        // 测试2: 归还日期计算的修复
        await testReturnDateCalculation();
        
        // 测试3: 多浏览器阅读场景
        await testMultiBrowserReading();
        await testReserveToBorrowConcurrency();
        
        console.log('\n=== 所有测试完成 ===');
        
    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        // 关闭数据库连接
        setTimeout(() => {
            mongoose.connection.close();
            process.exit(0);
        }, 2000);
    }
}

async function testBatchReturnReadingBooks() {
    console.log('\n--- 测试1: 批量归还阅读记录的修复 ---');
    
    // 查找一些未归还的阅读记录
    const readings = await Reading.find({ returned: false }).limit(3);
    console.log(`找到 ${readings.length} 个未归还的阅读记录`);
    
    if (readings.length > 0) {
        console.log('调用 batchReturnReadingBooks...');
        await batchReturnReadingBooks();
        console.log('batchReturnReadingBooks 执行完成');
        
        // 检查是否有记录被正确处理
        for (let reading of readings) {
            const updatedReading = await Reading.findById(reading._id);
            if (updatedReading.returned) {
                console.log(`✅ 阅读记录 ${reading._id} 已正确归还`);
            } else {
                console.log(`⏳ 阅读记录 ${reading._id} 尚未归还（可能未满足归还条件）`);
            }
        }
    } else {
        console.log('没有找到未归还的阅读记录');
    }
}

async function testReturnDateCalculation() {
    console.log('\n--- 测试2: 归还日期计算的修复 ---');
    
    // 模拟用户时区偏移（香港 UTC+8 = 480分钟）
    const userOffset = 480;
    const currentDate = new Date();
    
    console.log(`当前UTC时间: ${currentDate.toISOString()}`);
    
    // 模拟修复后的归还日期计算逻辑
    let userCurrentDate = new Date(currentDate.getTime() + userOffset * 60000);
    userCurrentDate.setUTCDate(userCurrentDate.getUTCDate() + MAX_BORROW_DAYS);
    userCurrentDate.setUTCHours(23, 59, 0, 0);
    
    // 转换回UTC时间存储到数据库
    let return_date = new Date(userCurrentDate.getTime() - userOffset * 60000);
    
    // 邮件显示日期
    let weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    let datetoshow = `${userCurrentDate.getUTCDate()}-${userCurrentDate.getUTCMonth() + 1}-${userCurrentDate.getUTCFullYear()} (${weekDays[userCurrentDate.getUTCDay()]})`;
    
    console.log(`用户时区当前时间: ${userCurrentDate.toISOString()}`);
    console.log(`数据库存储的归还日期: ${return_date.toISOString()}`);
    console.log(`邮件显示的归还日期: ${datetoshow}`);
    
    // 验证日期一致性
    const dbReturnDateInUserTz = new Date(return_date.getTime() + userOffset * 60000);
    const dbDateString = `${dbReturnDateInUserTz.getUTCDate()}-${dbReturnDateInUserTz.getUTCMonth() + 1}-${dbReturnDateInUserTz.getUTCFullYear()}`;
    const emailDateString = datetoshow.split(' ')[0];
    
    if (dbDateString === emailDateString) {
        console.log('✅ 数据库日期与邮件日期一致');
    } else {
        console.log(`❌ 数据库日期 (${dbDateString}) 与邮件日期 (${emailDateString}) 不一致`);
    }
}

async function testMultiBrowserReading() {
    console.log('\n--- 测试3: 多浏览器阅读场景 ---');
    
    // 查找一本有库存的书
    const book = await Book.findOne({ 
        available_quantity: { $gt: 0 },
        stock_quantity: { $gte: 2 }
    });
    
    if (!book) {
        console.log('没有找到合适的测试书籍');
        return;
    }
    
    console.log(`测试书籍: ${book.title} (ID: ${book._id})`);
    console.log(`初始库存: ${book.available_quantity}/${book.stock_quantity}`);
    
    // 模拟多个浏览器阅读
    const clientIds = ['browser_a_client_1', 'browser_b_client_1'];
    const readingIds = [];
    
    for (let clientId of clientIds) {
        // 检查库存
        const stockBefore = await BookHelper.isBookInStock(book._id);
        console.log(`${clientId} 开始阅读前库存: ${stockBefore}`);
        
        // 创建阅读记录
        const reading = new Reading({
            client_id: clientId,
            book_id: book._id,
            returned: false,
            read_date: new Date(),
            return_date: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后
            ip: '127.0.0.1'
        });
        await reading.save();
        readingIds.push(reading._id);
        
        // 更新库存
        await BookHelper.updateAvailableStock(book._id, 'reading');
        
        const stockAfter = await BookHelper.isBookInStock(book._id);
        console.log(`${clientId} 开始阅读后库存: ${stockAfter}`);
    }
    
    // 模拟关闭一个浏览器
    console.log('\n模拟关闭第一个浏览器...');
    const firstReadingId = readingIds[0];
    
    // 创建阅读记录（模拟超时）
    const readingRecord = new ReadingRecord({
        reading_id: firstReadingId,
        read_time: new Date(Date.now() - 5 * 60 * 1000) // 5分钟前
    });
    await readingRecord.save();
    
    const stockBeforeBatch = await BookHelper.isBookInStock(book._id);
    console.log(`批量归还前库存: ${stockBeforeBatch}`);
    
    // 执行批量归还
    await batchReturnReadingBooks();
    
    const stockAfterBatch = await BookHelper.isBookInStock(book._id);
    console.log(`批量归还后库存: ${stockAfterBatch}`);
    
    // 清理测试数据
    await Reading.deleteMany({ _id: { $in: readingIds } });
    await ReadingRecord.deleteOne({ reading_id: firstReadingId });
    
    // 恢复库存
    for (let i = 0; i < readingIds.length; i++) {
        const reading = await Reading.findById(readingIds[i]);
        if (!reading || !reading.returned) {
            await BookHelper.updateAvailableStock(book._id, 'return');
        }
    }
    
    const finalStock = await BookHelper.isBookInStock(book._id);
    console.log(`清理后最终库存: ${finalStock}`);
    
    if (finalStock === book.available_quantity) {
        console.log('✅ 库存恢复正常');
    } else {
        console.log('❌ 库存未完全恢复');
    }
}

async function testReserveToBorrowConcurrency() {
    console.log('\n--- 测试4: 预约转自动借阅并发库存同步 ---');
    // 假设有两用户A、B，A正在借阅，B预约
    const book = await Book.findOne({ available_quantity: { $gt: 0 }, stock_quantity: { $gte: 2 } });
    if (!book) {
        console.log('没有找到合适的测试书籍');
        return;
    }
    // 用户A借阅
    const userA = await User.findOne();
    const borrowA = new Borrow({
        user_id: userA._id,
        book_id: book._id,
        issue_date: new Date(),
        return_date: new Date(Date.now() + MAX_BORROW_DAYS * 24 * 60 * 60 * 1000),
        returned: false
    });
    await borrowA.save();
    await BookHelper.updateAvailableStock(book._id, 'borrow');
    // 用户B预约
    const userB = await User.findOne({ _id: { $ne: userA._id } });
    const reserveB = new Reserve({
        user_id: userB._id,
        book_id: book._id,
        reserve_date: new Date(),
        status: true,
        is_deleted: false
    });
    await reserveB.save();
    // 并发模拟：A阅读中，B预约，A归还后自动借入B
    // 先让A归还
    borrowA.returned = true;
    await borrowA.save();
    await BookHelper.updateAvailableStock(book._id, 'return');
    // 触发自动借阅
    const { syncReturnBooks } = require('./Routes/Borrow');
    await syncReturnBooks(book._id);
    // 检查库存
    const stock = await BookHelper.isBookInStock(book._id);
    console.log(`自动借阅后库存: ${stock}`);
    if (stock === 0) {
        console.log('✅ 预约转借阅后库存归零');
    } else {
        console.log('❌ 预约转借阅后库存未归零');
    }
    // 清理
    await Borrow.deleteMany({ user_id: { $in: [userA._id, userB._id] }, book_id: book._id });
    await Reserve.deleteMany({ user_id: userB._id, book_id: book._id });
    await BookHelper.isBookInStock(book._id); // 强制重算库存
}
