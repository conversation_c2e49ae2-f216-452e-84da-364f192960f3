# ESLint 配置指南

## 概述

ESLint 已配置用于检测类似 `book_id` 未定义变量的问题，防止类似的 bug 再次出现。

## 🔥 关键规则说明

### 1. `no-undef` (error)
**作用**: 检测未定义的变量
**示例**:
```javascript
// ❌ 错误 - ESLint 会报错
await syncReturnBooks(book_id);  // 'book_id' is not defined

// ✅ 正确
await syncReturnBooks(reading.book_id._id);
```

### 2. `no-unused-vars` (error)
**作用**: 检测声明但未使用的变量
**忽略规则**:
- 变量名匹配 `^(moment|_|unused|temp|debug).*$` 的会被忽略
- 参数名匹配 `^(err|error|_|req|res|next)$` 的会被忽略

**示例**:
```javascript
// ❌ 错误 - ESLint 会警告
const unusedVariable = 'hello';

// ✅ 正确 - 会被忽略
const moment = require('moment');        // 以 moment 开头
const _tempData = 'temp';               // 以 _ 开头
const unusedCounter = 0;                // 以 unused 开头
const debugInfo = 'debug';              // 以 debug 开头

// ✅ 正确 - 回调参数会被忽略
app.post('/api', (req, res, next) => {  // req, res, next 会被忽略
    // 即使不使用 req, res, next 也不会报错
});

function callback(err, data) {          // err 会被忽略
    if (data) console.log(data);
}
```

### 3. `import/order` (error)
**作用**: 强制 CommonJS require() 语句的排序和分组
**排序规则**:
1. **builtin** - Node.js 内置模块 (fs, path, etc.)
2. **external** - 第三方包 (express, mongoose, etc.)
3. **internal** - 项目内部模块 (使用别名 @/ 等)
4. **parent** - 父级目录 (../)
5. **sibling** - 同级目录 (./)
6. **index** - index 文件

**示例**:
```javascript
// ❌ 错误 - 顺序混乱
const User = require("../Model/User");      // parent
const mongoose = require('mongoose');       // external
const fs = require('fs');                  // builtin

// ✅ 正确 - 按规则排序
const fs = require('fs');                  // builtin
const path = require('path');              // builtin
const express = require('express');        // external
const mongoose = require('mongoose');      // external
const User = require("../Model/User");     // parent
```

### 4. `no-trailing-spaces` (error)
**作用**: 禁止行尾空格，包括空行中的空格
**配置**:
- `skipBlankLines: false` - 空行也不能有空格
- `ignoreComments: false` - 注释行也不能有行尾空格

**示例**:
```javascript
// ❌ 错误 - 行尾有空格
const data = 'hello';
const name = 'world';

// 空行中也有空格

// ✅ 正确 - 无行尾空格
const data = 'hello';
const name = 'world';

// 空行干净无空格

```

### 5. `eol-last` (error)
**作用**: 文件末尾必须有且只有一个换行符
**示例**:
```javascript
// ❌ 错误 - 文件末尾没有换行符
module.exports = data;[EOF]

// ✅ 正确 - 文件末尾有一个换行符
module.exports = data;
[EOF]
```

### 6. `no-multiple-empty-lines` (error)
**作用**: 控制连续空行的数量
**配置**:
- `max: 2` - 最多允许2个连续空行
- `maxEOF: 1` - 文件末尾最多1个空行
- `maxBOF: 0` - 文件开头不允许空行

**示例**:
```javascript
// ❌ 错误 - 超过2个连续空行
const a = 1;




const b = 2;

// ✅ 正确 - 最多2个连续空行
const a = 1;


const b = 2;
```

### 7. `no-use-before-define` (error)
**作用**: 要求在使用变量前先声明
**示例**:
```javascript
// ❌ 错误
console.log(myVar);  // 使用前未声明
let myVar = 'test';

// ✅ 正确
let myVar = 'test';
console.log(myVar);
```

## 🚀 使用方法

### 安装依赖
```bash
npm install --save-dev eslint
```

### 运行检查
```bash
# 检查所有文件
npm run lint

# 自动修复可修复的问题
npm run lint:fix

# 手动运行提交前检查
npm run precommit
```

### 设置 Git Hook
```bash
# 运行设置脚本
bash setup-git-hooks.sh
```

## 🔧 IDE 集成

### VS Code
1. 安装 ESLint 扩展
2. 在设置中启用 `eslint.autoFixOnSave`
3. 保存时自动显示错误并修复

### WebStorm/IntelliJ
1. 启用 ESLint 插件
2. 配置自动检查
3. 实时显示错误提示

## 📋 检查结果示例

运行 `npm run lint` 后的输出：
```
/path/to/Routes/Reading.js
  319:30  error  'book_id' is not defined  no-undef

✖ 1 problem (1 error, 0 warnings)
```

## 🛡️ 防护机制

1. **开发时**: IDE 实时提示错误
2. **提交时**: Git pre-commit hook 自动检查
3. **CI/CD**: 可集成到构建流程中

## 📝 自定义配置

如需修改规则，编辑 `.eslintrc.js`:
```javascript
rules: {
    'no-undef': 'error',        // 改为 'warn' 降低严重程度
    'no-unused-vars': 'warn',   // 改为 'off' 禁用规则
}
```

## 🎯 针对此次问题的检测

原始错误代码：
```javascript
await syncReturnBooks(book_id);  // book_id 未定义
```

ESLint 检测结果：
```
error: 'book_id' is not defined (no-undef)
```

修复后代码：
```javascript
await syncReturnBooks(reading.book_id._id);  // ✅ 通过检查
```

## 📚 更多资源

- [ESLint 官方文档](https://eslint.org/)
- [ESLint 规则列表](https://eslint.org/docs/rules/)
- [no-undef 规则详情](https://eslint.org/docs/rules/no-undef)
