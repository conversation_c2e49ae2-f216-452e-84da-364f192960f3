<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a64dc570-1ad1-4b1b-bee6-491b88153052" name="Changes" comment="fix: return_date">
      <change beforePath="$PROJECT_DIR$/Routes/Book.js" beforeDir="false" afterPath="$PROJECT_DIR$/Routes/Book.js" afterDir="false" />
    </list>
    <list id="3e83691a-3c8f-4a33-bdd1-2232dfc9acd0" name="cc" comment="">
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/depoly-uat.sh" beforeDir="false" afterPath="$PROJECT_DIR$/depoly-uat.sh" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30Am1pHRVzu4wmhT9a95F7ukVJo" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "uat",
    "last_opened_file_path": "E:/HXL_Book/jrc-service",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "yarn",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "C:\\Program Files\\JetBrains\\WebStorm 2025.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\HXL_Book\jrc-service" />
      <recent name="E:\HXL_Book\jrc-service\zlocal" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\HXL_Book\jrc-service\zlocal" />
    </key>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a64dc570-1ad1-4b1b-bee6-491b88153052" name="Changes" comment="fix: sendBorrowMail" />
      <created>1753078540667</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753078540667</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix: bookHelper, valid stock, update stock" />
    <MESSAGE value="fix: spell" />
    <MESSAGE value="fix: stock update sync" />
    <MESSAGE value="fix: continue next reading, $inc" />
    <MESSAGE value="fix: return_date" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: return_date" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/Routes/Borrow.js</url>
          <line>24</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>