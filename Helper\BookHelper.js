const Borrow = require("../Model/Borrow");
const Favourite = require("../Model/Favourite");
const moment = require("moment");
const Book = require("../Model/Book");
const Preview = require("../Model/Preview");
const Reserve = require("../Model/Reserve");
const Admin = require("../Model/Admin");
const Category = require("../Model/Category");
const Reading = require("../Model/Reading");
var mongoose = require('mongoose');
const async = require("async");
const R = require('ramda')
async function isBookBorrowed(userId, bookId) {
  const borrows = await Borrow.find({
    user_id: mongoose.Types.ObjectId(userId),
    book_id: mongoose.Types.ObjectId(bookId),
    returned: false,
  });

  if (!borrows) return false;

  let isBorrowed = false;
  for (let borrow of borrows) {
    var issue = new Date(borrow.issue_date);
    issue.setDate(issue.getDate() - 1);
    issue.setHours(0, 0, 0, 0);
    if (
      moment().isBetween(
        moment(issue),
        moment(new Date(borrow.return_date))
      )
    )
      isBorrowed = true;
  }

  return isBorrowed;
}

async function isBookFav(userId, bookId) {
  const favourite = await Favourite.find({
    user_id: mongoose.Types.ObjectId(userId),
    book_id: mongoose.Types.ObjectId(bookId)
  }).countDocuments();

  if (favourite == 0) return false;

  let isfav = true;
  return isfav;
}

async function isBookReserved(bookId) {
  const Reserved = await Reserve.find({
    book_id: mongoose.Types.ObjectId(bookId),
    is_deleted: false,
  }).countDocuments();
  if (Reserved == 0) return false;
  let isReserved = true;
  return isReserved;
}

async function totalBookBorrowedInPresent(userId, collection_type) {
  var totalBookBorrow = 0;

  
  // 全部借阅记录
  const borrowed = await Borrow.find({
    user_id: new mongoose.Types.ObjectId(userId),
    returned: false
  }, ['book_id'])
  // 对应collection下借阅数量
  const _borrowed = await Book.countDocuments({
    _id: {
      $in: (borrowed || []).map(x => new mongoose.Types.ObjectId(x.book_id))
    },
    collection_type
  })
  if (_borrowed) return _borrowed;
  return totalBookBorrow;
}

async function totalBookReservedInPresent(userId, collection_type) {
  var totalBookReserved = 0;
  const reserved = await Reserve.find({
    user_id: userId,
    is_deleted: false
  }, ['book_id'])
  const _reserved = await Book.countDocuments({
    _id: {
      $in: (reserved || []).map(
        (x) => new mongoose.Types.ObjectId(x.book_id)
      ),
    },
    collection_type,
  });
  if (_reserved) return _reserved;
  return totalBookReserved;
}

async function recommendedBooks(bookId, collection_type) {
  const _match = {
    _id: { $ne: (bookId) },
    $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],
    book_recomm: true
  }
  if (collection_type) Object.assign(_match, { collection_type });
  var result = await Book.aggregate([{
    $match: _match
  },
  {
    $project: {
      "title": "$title",
      "author": "$author",
      "book_recomm_datetime": "$book_recomm_datetime",
      "cover_photo": "$cover_photo"
    }
  },
  {
    $sort: { "book_recomm_datetime": -1 }
  }
  ]);
  return result;
}
async function isTimeWithinReBorrowPeriod(borrowId) {
  const borrowData = await Borrow.findOne({
    _id: borrowId
  });
  if (!borrowData) return false;

  const twoDays = 2 * 24 * 60 * 60 * 1000;
  const currentTime = new Date().getTime();
  const returnTime = new Date(borrowData.return_date).getTime();
  // 超过归还时间，不可借
  if (currentTime > returnTime) return false;
  // 超过2天，不可续借
  return returnTime - currentTime <= twoDays;
}

async function hasAvalibleBookCopies(bookId) {
  const books = await Book.findOne({
    _id: mongoose.Types.ObjectId(bookId),
  });
  return books.available_quantity && books.available_quantity > 0;
}

async function isReBorrowAllowed(borrowedId) {
  const isInTimeRange = await isTimeWithinReBorrowPeriod(borrowedId);
  if (!isInTimeRange) return false;
  const borrowData = await Borrow.findOne({
    _id: borrowedId
  });
  if (!borrowData) return false;
  var book_id = borrowData.book_id;
  const isBookReserve = await isBookReserved(book_id);
  const hasCopies = await hasAvalibleBookCopies(book_id);
  if (!hasCopies && isBookReserve) return false;
  return !borrowData.reborrowed_twice;
}

async function isBookInStock(bookId) {
  try {
    // 1. 获取书籍信息
    const books = await Book.findOne({
      _id: mongoose.Types.ObjectId(bookId)
    });

    if (!books) {
      console.log(`Book not found: ${bookId}`);
      return 0;
    }

    // 2. 如果 available_quantity <= 0，需要重新计算
    if (books.available_quantity <= 0) {
      console.log(`Recalculating stock for book ${bookId}, current available_quantity: ${books.available_quantity}`);

      // 3. 等待计算借阅数量
      const borrows = await Borrow.countDocuments({
        book_id: mongoose.Types.ObjectId(bookId),
        returned: false
      });

      // 4. 等待计算阅读中的数量
      const readings = await Reading.countDocuments({
        book_id: mongoose.Types.ObjectId(bookId),
        returned: false
      });

      // 5. 重新计算可用数量
      let available_quantity = parseInt(books.stock_quantity) - parseInt(borrows) - parseInt(readings);
      if (available_quantity < 0) available_quantity = 0;

      console.log(`Stock calculation - Total: ${books.stock_quantity}, Borrowed: ${borrows}, Reading: ${readings}, Available: ${available_quantity}`);

      // 6. 更新数据库中的 available_quantity
      await Book.findOneAndUpdate({
        _id: mongoose.Types.ObjectId(bookId)
      }, {
        available_quantity: available_quantity
      });

      // 7. 返回重新计算后的值
      return available_quantity;
    }

    // 8. 返回当前的 available_quantity
    return books.available_quantity;
  } catch (error) {
    console.error('Error in isBookInStock:', error);
    return 0;
  }
}

async function updateAvailableStock(bookId, type) {
  const books = await Book.findOne({
    _id: mongoose.Types.ObjectId(bookId)
  });

  if (!books) {
    console.log(`Book not found: ${bookId}`);
    return;
  }

  const originalQuantity = books.available_quantity;
  console.log(`updateAvailableStock - Book: ${bookId}, Type: ${type}, Current stock: ${originalQuantity}/${books.stock_quantity}`);

  // If available_quantity column is not exist in table then it will update this
  if (["reading", "borrow"].includes(type)) {
    // 原子操作，防止并发下库存不一致
    const result = await Book.findOneAndUpdate({
      _id: mongoose.Types.ObjectId(bookId),
      available_quantity: { $gt: 0 }
    }, {
      $inc: { available_quantity: -1 }
    }, { new: true });
    if (result) {
      console.log(`Stock decreased for ${type}: ${originalQuantity} -> ${result.available_quantity}`);
    } else {
      console.log(`Stock decrease failed for ${type}: 当前库存为0，未减少`);
    }
    return;
  }
  if (type == "return") {
    if(originalQuantity < books.stock_quantity){
      const available_quantity = parseInt(originalQuantity) + 1;
      await Book.findOneAndUpdate({
        _id: mongoose.Types.ObjectId(bookId)
      }, {
        available_quantity
      });
      console.log(`Stock increased for return: ${originalQuantity} -> ${available_quantity}`);
      return;
    }
    console.log(`Stock already at maximum (${books.stock_quantity}), no increase needed`);
  }
}

async function renewBooksbydateV2(startDate, endDate, email, collection_type) {
  //  const _startDate = moment(startDate)
  //    .tz("Asia/Hong_Kong")
  //    .startOf("d")
  //    .toDate();
  //  const _endDate = moment(endDate).tz("Asia/Hong_Kong").endOf("d").toDate();

  const admin = await Admin.findOne({
    email,
  });

  const firstMatchCondition = {
    "books.collection_type": collection_type,
    reborrowed_once: true,
    reborrow_one_date: {
      $gte: new Date(startDate),
      $lte: new Date(endDate),
    },
  };

  const secondMatchCondition = {
    "books.collection_type": collection_type,
    reborrowed_twice: true,
    reborrow_two_date: {
      $gte: new Date(startDate),
      $lte: new Date(endDate),
    },
  };
  if (admin.role !== "1" && admin.role !== "2") {
    Object.assign(firstMatchCondition, {
      "books.added_by": admin._id,
    });

    Object.assign(secondMatchCondition, {
      "books.added_by": admin._id,
    });
  }

  const firstAggregateCondition = [
    {
      $lookup: {
        from: "books",
        localField: "book_id",
        foreignField: "_id",
        as: "books",
      },
    },
    {
      $match: firstMatchCondition,
    },
    {
      $group: {
        _id: {
          book_id: "$book_id",
        },
        count: {
          $sum: 1,
        },
      },
    },
    {
      $project: {
        book_id: "$_id.book_id",
        count: "$count",
        _id: 0,
      },
    },
  ];

  const firstData = await Borrow.aggregate(firstAggregateCondition);
  const secondAggregateCondition = [
    {
      $lookup: {
        from: "books",
        localField: "book_id",
        foreignField: "_id",
        as: "books",
      },
    },
    {
      $match: secondMatchCondition,
    },
    {
      $group: {
        _id: {
          book_id: "$book_id",
        },
        count: {
          $sum: 1,
        },
      },
    },
    {
      $project: {
        book_id: "$_id.book_id",
        count: "$count",
        _id: 0,
      },
    },
  ];

  const secondData = await Borrow.aggregate(secondAggregateCondition);

  const result = R.groupBy((x) => x.book_id, [].concat(firstData || [], secondData || []));

  const allBooks = await Book.find({
    _id: {
      $in: Object.keys(result).map((x) => mongoose.Types.ObjectId(x)),
    },
  });

  const categories = await Category.find({});

  let items = Object.keys(result)
    .map((x) => {
      const books = allBooks.find((y) => y._id.toString() === x.toString());
      const category = books.category_id.map((y) => {
        const category = categories.find(
          (category) => category._id.toString() === y.toString()
        );
        return category ? category.name : " ";
      });
      const counts = R.sum(result[x].map((y) => y.count));
      return {
        books,
        category,
        counts,
      };
    })
    .sort((a, b) => b.counts - a.counts);

  const otherBooks = await Book.aggregate([
    {
      $match: {
        $and: [
          {
            $or: [
              { is_deleted: { $exists: false } },
              { is_deleted: { $exists: true, $eq: false } },
            ],
          },
          {
            collection_type: collection_type,
            isbn_no: {
              $nin: (items || []).map(x => (x.books || {}).isbn_no).filter(x => !!x)
            }
          }
        ]
      }
    },
    {
      $lookup: {
        from: "categories",
        localField: "category_id",
        foreignField: "_id",
        as: "categories",
      },
    }
  ])

  items = items.concat(otherBooks.map(x => ({ books: x, category: x.categories.map(y => y.name || ''), counts: 0 })));

  return items

}


async function renewBooksCountbydateV2(
  startDate,
  endDate,
  email,
  collection_type
) {
  const _startDate = moment(startDate)
    .tz("Asia/Hong_Kong")
    .startOf("d")
    .toDate();
  const _endDate = moment(endDate).tz("Asia/Hong_Kong").endOf("d").toDate();

  const admin = await Admin.findOne({
    email,
  });

  const firstMatchCondition = {
    "books.collection_type": collection_type,
    reborrowed_once: true,
    reborrow_one_date: {
      $gte: _startDate,
      $lte: _endDate,
    },
  };

  const secondMatchCondition = {
    "books.collection_type": collection_type,
    reborrowed_twice: true,
    reborrow_two_date: {
      $gte: _startDate,
      $lte: _endDate,
    },
  };
  if (admin.role !== "1" && admin.role !== "2") {
    Object.assign(firstMatchCondition, {
      "books.added_by": admin._id,
    });

    Object.assign(secondMatchCondition, {
      "books.added_by": admin._id,
    });
  }

  const firstAggregateCondition = [
    {
      $lookup: {
        from: "books",
        localField: "book_id",
        foreignField: "_id",
        as: "books",
      },
    },
    {
      $match: firstMatchCondition,
    },
    {
      $group: {
        "_id": {
          "book_id": "$book_id",
        },
        count: {
          $sum: 1
        }
      }
    },
    {
      $project: {
        book_id: "$_id.book_id",
        count: "$count",
        _id: 0
      }
    }
  ];

  const firstData = await Borrow.aggregate(firstAggregateCondition);
  const secondAggregateCondition = [
    {
      $lookup: {
        from: "books",
        localField: "book_id",
        foreignField: "_id",
        as: "books",
      },
    },
    {
      $match: secondMatchCondition,
    },
    {
      $group: {
        _id: {
          book_id: "$book_id",
        },
        count: {
          $sum: 1,
        },
      },
    },
    {
      $project: {
        book_id: "$_id.book_id",
        count: "$count",
        _id: 0,
      },
    },
  ];

  const secondData = await Borrow.aggregate(secondAggregateCondition);

  const result = R.groupBy(
    (x) => x.book_id,
    [].concat(firstData, secondData)
  );

  return {
    renewedCopies: R.sum(R.flatten(Object.values(result)).map((x) => x.count)),
    renewedBooks: Object.keys(result).length
  };
}

/** 08-01-2022 -> Optimised **/
async function renewedBooksbydate(book_id, startDate, endDate, all, email, collection_type) {
  let catArray = await findCats();
  let sortedBooks = [];

  /** To find how many books is being reborrowed first time **/
  var first = 0;
  var second = 0;
  var booksCounts = [];
  var sbooksCounts = [];
  var booksids = [];
  var secondBooks = [];
  let fresult = await Borrow.aggregate([{
    $match: { reborrowed_once: true, reborrow_one_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) } }
  },
  {
    $lookup: {
      from: 'books',
      localField: 'book_id',
      foreignField: '_id',
      as: 'books'
    }
  },
  {
    $match: {
      "books.collection_type": collection_type
    }
  },
  {
    $group: {
      "_id": {
        "book_id": "$book_id"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $group: {
      "_id": null,
      "data": {
        "$push": {
          "book_id": "$_id.book_id",
          "count": "$count"
        }
      }
    }
  }]);
  if (fresult.length > 0) {
    let book_ids = [];
    let fdata = fresult[0].data;
    for (var j = 0; j < fdata.length; j++) {
      book_ids.push(fdata[j].book_id);
      first = parseInt(first) + fdata[j].count;
      booksCounts[j] = fdata[j].count;
    }
    booksids = [...book_ids, ...booksids];
  }

  let sresult = await Borrow.aggregate([{
    $match: { reborrowed_twice: true, reborrow_two_date: { "$gte": new Date(startDate), "$lte": new Date(endDate) } }
  },
  {
    $group: {
      "_id": {
        "book_id": "$book_id"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $group: {
      "_id": null,
      "data": {
        "$push": {
          "book_id": "$_id.book_id",
          "count": "$count"
        }
      }
    }
  }]);

  if (sresult.length > 0) {
    let sdata = sresult[0].data;
    for (var j = 0; j < sdata.length; j++) {
      secondBooks.push(sdata[j].book_id);
      second = parseInt(second) + sdata[j].count;
      sbooksCounts[j] = sdata[j].count;
    }
  }

  if (booksids.length > 0 && secondBooks.length > 0) {
    for (var i = 0; i < secondBooks.length; i++) {
      let isExist = false;
      for (var j = 0; j < booksids.length; j++) {
        if (((secondBooks[i]).toString()).toLowerCase() == ((booksids[j].toString()).toLowerCase())) {
          isExist = true;
          booksCounts[j] = parseInt(booksCounts[j]) + parseInt(sbooksCounts[i]);
        }
      }
      if (isExist == false) {
        booksids.push(secondBooks[i]);
        booksCounts.push(sbooksCounts[i]);
      }
    }
  }
  let resx = await Admin.findOne({
    email: email
  });

  let c = 0;
  let recommendedBooks = [];
  if (booksids.length > 0) {
    for (let booksid of booksids) {
      await Book.findOne({ _id: mongoose.Types.ObjectId(booksid) }).then(async (bookRes) => {
        let category_ids = (bookRes.category_id);
        let isbn_no = (bookRes.isbn_no);
        let publishingGroup = (bookRes.publishingGroup);
        let imprints = (bookRes.imprints);
        let title = (bookRes.title);
        let author = (bookRes.author);
        let added_by = (bookRes.added_by);

        let books = {
          author: author,
          added_by: added_by,
          title: title,
          publishingGroup: publishingGroup,
          imprints: imprints,
          isbn_no: isbn_no,
        };
        var categoryList = [];
        await category_ids.map(async (el) => {
          if (catArray.length > 0) {
            await catArray.map(async (allCats) => {
              if (typeof allCats[el.toString()] != 'undefined') {
                categoryList.push(allCats[el.toString()]);
              }
            });
          }
        });

        if (resx.role == '3' && ((books.added_by).toString()) == ((resx._id).toString())) {
          recommendedBooks.push({
            books: books,
            category: categoryList,
            counts: booksCounts[c]
          });
        } else if (resx && (resx.role == '1' || resx.role == '2')) {
          recommendedBooks.push({
            books: books,
            category: categoryList,
            counts: booksCounts[c]
          });
        }
        c++;
      });
    }
  }

  recommendedBooks = recommendedBooks.sort((a, b) => b.counts - a.counts);
  return recommendedBooks;
}
/** 08-01-2022 -> Optimised **/
async function reservedBooksbydate(book_id, startDate, endDate, all, email, collection_type) {
  let catArray = await findCats();
  let sortedBooks = [];
  let result = await Reserve.aggregate([{
    $match: {
      "book_id": {
        $ne: book_id
      },
      reserve_date: {
        "$gte": new Date(startDate),
        "$lte": new Date(endDate)
      }
    }
  },
  {
    $lookup: {
      from: 'books',
      localField: 'book_id',
      foreignField: '_id',
      as: 'books'
    }
  },
  {
    $match: {
      "books.collection_type": collection_type
    }
  },
  {
    $unwind: "$books"
  },
  {
    $group: {
      "_id": {
        "book_id": "$book_id",
        "books": "$books"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $group: {
      "_id": null,
      "data": {
        "$push": {
          "books": {
            "book_id": "$_id.books._id",
            "isbn_no": "$_id.books.isbn_no",
            "title": "$_id.books.title",
            "author": "$_id.books.author",
            "added_by": "$_id.books.added_by",
            "publishingGroup": "$_id.books.publishingGroup",
            "imprints": "$_id.books.imprints",
            "category_id": "$_id.books.category_id"
          },
          "book_id": "$_id.book_id",
          "count": "$count"
        }
      }
    }
  },
  {
    $project: {
      data: 1
    }
  },
  {
    $sort: {
      'data.book_id': -1
    }
  }
  ]);
  if (result && result.length > 0) {
    sortedBooks = [...result[0].data, ...sortedBooks];
  }
  let recommendedBooks = [];
  if (sortedBooks.length > 0) {
    sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
    let resx = await Admin.findOne({
      email: email
    });
    for (let sortedBook of sortedBooks) {
      let book_id = sortedBook.book_id;
      var books = sortedBook.books;
      var book_count = sortedBook.count;

      var category_ids = books.category_id;
      var categoryList = [];
      await category_ids.map(async (el) => {
        if (catArray.length > 0) {
          await catArray.map(async (allCats) => {
            if (typeof allCats[el.toString()] != 'undefined') {
              categoryList.push(allCats[el.toString()]);
            }
          });
        }
      });

      if (resx.role == '3' && ((books.added_by).toString()) == ((resx._id).toString())) {
        recommendedBooks.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      } else if (resx && (resx.role == '1' || resx.role == '2')) {
        recommendedBooks.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      }
    }
    return recommendedBooks;
  } else {
    return recommendedBooks;
  }
}

/** 10-02-2022 -> Optimised **/
async function getunusedBookBydate(book_id, startDate, endDate, all, email, collection_type) {
  var bookstop = [];
  var borrowedBooks = await Reserve.find({
    book_id: {
      $ne: book_id
    }
  });
  borrowedBooks.forEach((el) => {
    if ((new Date(el.reserve_date).setHours(0, 0, 0, 0)) >= new Date(startDate).setHours(0, 0, 0, 0) && (new Date(el.reserve_date).setHours(0, 0, 0, 0)) <= new Date(endDate).setHours(0, 0, 0, 0)) {
      bookstop.push(el);
    }
  });

  borrowedBooks = await Borrow.find({
    book_id: {
      $ne: book_id
    }
  });
  borrowedBooks.forEach((el) => {
    if ((new Date(el.issue_date).setHours(0, 0, 0, 0)) >= new Date(startDate).setHours(0, 0, 0, 0) && (new Date(el.issue_date).setHours(0, 0, 0, 0)) <= new Date(endDate).setHours(0, 0, 0, 0)) {
      bookstop.push(el);
    }
  });

  borrowedBooks = await Borrow.find({
    book_id: {
      $ne: book_id
    },
    reborrowed_once: true,
  });
  borrowedBooks.forEach((el) => {
    if ((new Date(el.reborrow_one_date).setHours(0, 0, 0, 0)) >= new Date(startDate).setHours(0, 0, 0, 0) && (new Date(el.reborrow_one_date).setHours(0, 0, 0, 0)) <= new Date(endDate).setHours(0, 0, 0, 0)) {
      bookstop.push(el);
    }
  });

  const uniqueBooks = Array.from(
    new Set(bookstop.map((item) => item.book_id))
  );
  const uniqueBookCounts = [];

  var uniqueArray = [];
  for (i = 0; i < uniqueBooks.length; i++) {
    if (uniqueArray.indexOf(uniqueBooks[i].toString()) === -1) {
      uniqueArray.push(uniqueBooks[i].toString());
    }
  }

  for (let book of uniqueArray) {
    uniqueBookCounts.push(book);
  }
  const sortedBooks = uniqueBookCounts.sort((a, b) => b.count - a.count);
  if (all == 0) {
    if (sortedBooks.length > 10) sortedBooks.length = 10;
  }

  const recommendedBooks = [];
  await Admin.findOne({
    email: email
  }).then(async (resx) => {
    if (resx.role == '3') {
      await Book.find({
        _id: {
          $nin: sortedBooks,
        },
        collection_type,
        available_quantity: {
          $gt: 0
        },
        $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
      }).then(async (rest) => {

        if (!rest) {
          return res.json({
            code: 422,
            message: "No data found",
            status: false,
          });
        }

        var promises1 = rest.map(async (el) => {
          var cateId = [];
          const promises = el.category_id.map(async (ell) => {
            var catss = await Category.findOne({
              _id: ell
            }).then(async (catRes) => {
              if (catRes && catRes != null) {
                cateId.push(catRes.name);
              }
            });
          });
          const results = await Promise.all(promises);
          let datas = {};
          datas.books = el;
          datas.category = cateId;
          recommendedBooks.push(datas);
        });
        const results = await Promise.all(promises1);
      });
    } else {
      await Book.find({
        _id: {
          $nin: sortedBooks
        },
        collection_type,
        available_quantity: {
          $gt: 0
        },
        $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }]
      }).then(async (rest) => {
        if (!rest) {
          return res.json({
            code: 422,
            message: "No data found",
            status: false,
          });
        }

        var promises1 = rest.map(async (el) => {
          var cateId = [];
          const promises = el.category_id.map(async (ell) => {
            var catss = await Category.findOne({
              _id: ell
            }).then(async (catRes) => {
              if (catRes && catRes != null) {
                cateId.push(catRes.name);
              }
            });
          });
          const results = await Promise.all(promises);
          let datas = {};
          datas.books = el;
          datas.category = cateId;
          recommendedBooks.push(datas);
        });
        const results = await Promise.all(promises1);
      });
    }
  });

  return recommendedBooks;
}

async function getunusedBookByDateV2(
  collection_type,
  startDate,
  endDate,
  email) {
  const _startDate = moment(startDate)
    .tz("Asia/Hong_Kong")
    .startOf("d")
    .toDate();
  const _endDate = moment(endDate)
    .tz("Asia/Hong_Kong")
    .endOf("d")
    .toDate();

  const splitTimePoint = new Date("2022-11-24T16:00:00.000Z");

  const admin = await Admin.findOne({ email });

  // reserve_date;

  // issue_date;

  const borrowedBooks = await Borrow.distinct("book_id", {
    issue_date: {
      $gte: _startDate,
      $lte: _endDate,
    },
  });
  const reservedBooks = await Reserve.distinct("book_id", {
    reserve_date: {
      $gte: _startDate,
      $lte: _endDate,
    },
  });
  // const previewedBooks = await Preview.distinct("book_id", {});

  // console.log("previewedBooks.length", previewedBooks.length);

  const usedBooks = Array.from(
    new Set([].concat(borrowedBooks, reservedBooks))
  );
  let condition = {
    _id: {
      $nin: usedBooks,
    },
    collection_type,
    available_quantity: {
      $gt: 0,
    }
  };

  if (_endDate.getTime() < splitTimePoint.getTime()) {
    Object.assign(condition, {
      added_at: {
        $lte: splitTimePoint,
      },
      $or: [
        {
          deleted_at: {
            $exists: false
          },
          deleted_at: {
            $gte: _startDate
          }
        }
      ]
    });
  }

  if (
    _startDate.getTime() < splitTimePoint.getTime() &&
    _endDate.getTime() >= splitTimePoint.getTime()
  ) {
    Object.assign(condition, {
      $or: [
        { is_deleted: { $exists: false } },
        { is_deleted: { $exists: true, $eq: false } },
      ],
    });
  }

  if (_startDate.getTime() >= splitTimePoint.getTime()) {
    Object.assign(condition, {
      $or: [
        { is_deleted: { $exists: false } },
        { is_deleted: { $exists: true, $eq: false } },
        { deleted_at: { $gte: _endDate } }
      ],
    });
  }

  if (admin.role != "1" && admin.role != '2') {
    Object.assign(condition, { added_by: admin._id });
  }
  const unusedBooks = await Book.find(condition);
  const categories = await Category.find({});

  return unusedBooks.map((x) => ({
    books: x,
    category: x.category_id.map(
      (y) => {
        const category = categories.find(
          (category) => category._id.toString() === y.toString()
        );
        return category ? category.name : ' '
      }
    ),
  }));
}

async function getunusedBookCountByDateV2(
  collection_type,
  startDate,
  endDate,
  email
) {
  const _startDate = moment(startDate)
    .tz("Asia/Hong_Kong")
    .startOf("d")
    .toDate();
  const _endDate = moment(endDate).tz("Asia/Hong_Kong").endOf("d").toDate();

  const splitTimePoint = new Date("2022-11-24T16:00:00.000Z");

  const admin = await Admin.findOne({ email });

  const borrowedBooks = await Borrow.distinct("book_id", {
    issue_date: {
      $gte: _startDate,
      $lte: _endDate,
    },
  });
  const reservedBooks = await Reserve.distinct("book_id", {
    reserve_date: {
      $gte: _startDate,
      $lte: _endDate,
    },
  });
  // const previewedBooks = await Preview.distinct("book_id", {});

  // console.log("previewedBooks.length", previewedBooks.length);

  const usedBooks = Array.from(
    new Set([].concat(borrowedBooks, reservedBooks))
  );
  let condition = {
    _id: {
      $nin: usedBooks,
    },
    collection_type,
    available_quantity: {
      $gt: 0,
    },
  };

  if (_endDate.getTime() < splitTimePoint.getTime()) {
    Object.assign(condition, {
      added_at: {
        $lte: splitTimePoint,
      },
      $or: [
        {
          deleted_at: {
            $exists: false
          },
          deleted_at: {
            $gte: _startDate
          }
        }
      ]
    });
  }

  if (
    _startDate.getTime() < splitTimePoint.getTime() &&
    _endDate.getTime() >= splitTimePoint.getTime()
  ) {
    Object.assign(condition, {
      $or: [
        { is_deleted: { $exists: false } },
        { is_deleted: { $exists: true, $eq: false } },
      ],
    });
  }

  if (_startDate.getTime() >= splitTimePoint.getTime()) {
    Object.assign(condition, {
      $or: [
        { is_deleted: { $exists: false } },
        { is_deleted: { $exists: true, $eq: false } },
        { deleted_at: { $gte: _endDate } }
      ],
    });
  }

  if (admin.role != "1" && admin.role != "2") {
    Object.assign(condition, { added_by: admin._id });
  }
  const count = await Book.countDocuments(condition);
  return count;
}

/** 08-01-2022 -> Optimised **/
async function previewBooksbydate(book_id, startDate, endDate, all, email, collection_type) {
  let catArray = await findCats();
  var sortedBooks = [];
  var result = await Preview.aggregate([{
    $match: {
      book_id: {
        $ne: book_id
      },
      created_on: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    }
  },
  {
    $lookup: {
      from: 'books',
      localField: 'book_id',
      foreignField: '_id',
      as: 'books'
    }
  },
  {
    $match: {
      "books.collection_type": collection_type
    }
  },
  {
    $unwind: "$books"
  },
  {
    $group: {
      "_id": {
        "book_id": "$book_id",
        "books": "$books"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $group: {
      "_id": null,
      "data": {
        "$push": {
          "books": {
            "book_id": "$_id.books._id",
            "isbn_no": "$_id.books.isbn_no",
            "title": "$_id.books.title",
            "author": "$_id.books.author",
            "added_by": "$_id.books.added_by",
            "category_id": "$_id.books.category_id",
            "publishingGroup": "$_id.books.publishingGroup",
            "imprints": "$_id.books.imprints"
          },
          "book_id": "$_id.book_id",
          "count": "$count"
        }
      }
    }
  },
  {
    $project: {
      data: 1
    }
  },
  {
    $sort: {
      'data.book_id': -1
    }
  }
  ]);

  if (result && result.length > 0) {
    sortedBooks = [...result[0].data, ...sortedBooks];
  }
  let recommendedBooks = [];
  if (sortedBooks.length > 0) {
    sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
    let resx = await Admin.findOne({
      email: email
    });

    for (let sortedBook of sortedBooks) {
      let book_id = sortedBook.book_id;
      var books = sortedBook.books;
      var book_count = sortedBook.count;

      var category_ids = books.category_id;
      var categoryList = [];
      await category_ids.map(async (el) => {
        if (catArray.length > 0) {
          await catArray.map(async (allCats) => {
            if (typeof allCats[el.toString()] != 'undefined') {
              categoryList.push(allCats[el.toString()]);
            }
          });
        }
      });

      if (resx.role == '3' && ((books.added_by).toString()) == ((resx._id).toString())) {
        recommendedBooks.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      } else if (resx && (resx.role == '1' || resx.role == '2')) {
        recommendedBooks.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      }
    }
  }

  const otherBooks = await Book.aggregate([
    {
      $match: {
        $and: [
          {
            $or: [
              { is_deleted: { $exists: false } },
              { is_deleted: { $exists: true, $eq: false } },
            ],
          },
          {
            collection_type: collection_type,
            isbn_no: {
              $nin: (recommendedBooks || []).map(x => (x.books || {}).isbn_no).filter(x => !!x)
            }
          }
        ]
      }
    },
    {
      $lookup: {
        from: "categories",
        localField: "category_id",
        foreignField: "_id",
        as: "categories",
      },
    }
  ])

  recommendedBooks = recommendedBooks.concat(otherBooks.map(x => ({ books: x, category: x.categories.map(y => y.name || ''), counts: 0 })));


  return recommendedBooks;
}

/** 05-01-2022 -> Optimised **/
async function recommendedBooksbydate(book_id, startDate, endDate, all, email, isPetronVisible, collection_type) {
  let resx;
  startDate = new Date(startDate);
  endDate = new Date(endDate);
  //await (Admin.findOne({email: email}, function(err, res){ resx = (res); }).select({ _id: 1, role: 1 }).lean().exec());
  let catArray = await findCats();

  let aggregate = [
    {
      $match: {
        "book_id": {
          $ne: (book_id) /* for frontend it will show all books except existing selected book */
        },
        issue_date: {
          "$gte": startDate,
          "$lte": endDate
        }
      }
    },
    {
      $lookup: {
        from: 'books',
        localField: 'book_id',
        foreignField: '_id',
        as: 'books'
      }
    },
    {
      $match: {
        "books.collection_type": collection_type,
        // $or:[
        //         {
        //             "books.is_deleted": {
        //                 $exists: false
        //             }
        //         },
        //         {
        //             "books.is_deleted":false 
        //         }
        //     ]
      }
    },
    {
      $unwind: "$books"
    },
    {
      $group: {
        "_id": {
          "book_id": "$book_id",
          "books": "$books"
        },
        count: {
          $sum: 1
        }
      }
    },
    {
      $group: {
        "_id": null,
        "data": {
          "$push": {
            "books": {
              "isbn_no": "$_id.books.isbn_no",
              "title": "$_id.books.title",
              "author": "$_id.books.author",
              "category_id": "$_id.books.category_id",
              "added_by": "$_id.books.added_by",
              "publishingGroup": "$_id.books.publishingGroup",
              "imprints": "$_id.books.imprints"
            },
            "book_id": "$_id.book_id",
            "count": "$count"
          }
        }
      }
    },
    {
      $sort: {
        'data.book_id': -1
      }
    }
  ];


  if (isPetronVisible == true) {
    aggregate = [
      {
        $match: {
          "book_id": {
            $ne: (book_id) /* for frontend it will show all books except existing selected book */
          },
          issue_date: {
            "$gte": startDate,
            "$lte": endDate
          }
        }
      },
      {
        $lookup: {
          from: 'books',
          localField: 'book_id',
          foreignField: '_id',
          as: 'books'
        }
      },
      {
        $match: {
          "books.collection_type": collection_type,
          // $or:[
          //     {
          //         "books.is_deleted": {
          //             $exists: false
          //         }
          //     },
          //     {
          //     "books.is_deleted":false 
          //     }
          // ]
        }
      },
      {
        $unwind: "$books"
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'users'
        }
      },
      {
        $unwind: "$users"
      },
      {
        $group: {
          "_id": {
            "book_id": "$book_id",
            "books": "$books",
            "users": "$users"
          },
          count: {
            $sum: 1
          }
        }
      },
      {
        $group: {
          "_id": null,
          "data": {
            "$push": {
              "books": {
                "isbn_no": "$_id.books.isbn_no",
                "title": "$_id.books.title",
                "author": "$_id.books.author",
                "category_id": "$_id.books.category_id",
                "added_by": "$_id.books.added_by",
                "publishingGroup": "$_id.books.publishingGroup",
                "imprints": "$_id.books.imprints",
                "patronid": "$_id.users.patronid"
              },
              "book_id": "$_id.book_id",
              "count": "$count"
            }
          }
        }
      },
      {
        $sort: {
          'data.book_id': -1
        }
      }
    ];
  }

  let sortedBooks = recommendedBooks = [];
  let resultobj = await Borrow.aggregate(aggregate).then(async function (result) {
    if (result && result.length > 0) {
      sortedBooks = result[0].data;
    }
    if (sortedBooks.length > 0) {
      sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
      await Admin.findOne({
        email: email
      }).then(async (resx) => {
        sortedBooks.map(async function (sortedBook) {
          let book_id = sortedBook.book_id;
          var books = sortedBook.books;
          var category_ids = books.category_id;

          var book_count = sortedBook.count;
          var categoryList = [];
          await category_ids.map(async (el) => {
            if (catArray.length > 0) {
              await catArray.map(async (allCats) => {
                if (typeof allCats[el.toString()] != 'undefined') {
                  categoryList.push(allCats[el.toString()]);
                }
              });
            }
          });

          if (resx && resx.role == '3' && ((books.added_by).toString()) == ((resx._id).toString())) {
            recommendedBooks.push({
              books: books,
              category: categoryList,
              counts: book_count
            });
          } else if (resx && (resx.role == '1' || resx.role == '2')) {
            recommendedBooks.push({
              books: books,
              category: categoryList,
              counts: book_count
            });
          }
        });
      });
    }
  });

  return (recommendedBooks);
}

async function getAllBooksuploaded(book_id, email, collection_type) {
  const recommendedBooks = [];
  await Admin.findOne({
    email: email
  }).then(async (resx) => {
    if (resx.role == '3') {
      await Book.find({
        collection_type,
        $or: [
          { is_deleted: { $exists: false } },
          { is_deleted: { $exists: true, $eq: false } },
        ],
      }).then(async (rest) => {
        if (!rest) {
          return res.json({
            code: 422,
            message: "No data found",
            status: false,
          });
        }

        var promises1 = rest.map(async (el) => {
          var cateId = [];
          const promises = el.category_id.map(async (ell) => {
            var catss = await Category.findOne({
              _id: ell,
            }).then(async (catRes) => {
              if (catRes && catRes != null) {
                cateId.push(catRes.name);
              }
            });
          });
          const results = await Promise.all(promises);
          let datas = {};
          datas.books = el;
          datas.category = cateId;
          recommendedBooks.push(datas);
        });
        const results = await Promise.all(promises1);
      });
    } else {

      await Book.find({
        collection_type,
        $or: [
          { is_deleted: { $exists: false } },
          { is_deleted: { $exists: true, $eq: false } },
        ],
      }).then(async (rest) => {
        if (!rest) {
          return res.json({
            code: 422,
            message: "No data found",
            status: false,
          });
        }

        var promises1 = rest.map(async (el) => {
          var cateId = [];
          const promises = el.category_id.map(async (ell) => {
            var catss = await Category.findOne({
              _id: ell,
            }).then(async (catRes) => {
              if (catRes && catRes != null) {
                cateId.push(catRes.name);
              }
            });
          });
          const results = await Promise.all(promises);
          let datas = {};
          datas.books = el;
          datas.category = cateId;
          recommendedBooks.push(datas);
        });
        const results = await Promise.all(promises1);
      });
    }
  });
  return recommendedBooks;
}

/** 18-01-2022 -> Optimised **/
async function recommBooksbydate(book_id, email, collection_type) {
  let catArray = await findCats();
  var sortedBooks = [];
  var result = await Book.aggregate([
    {
      $match: {
        _id: {
          $ne: book_id,
        },
        $or: [
          { is_deleted: { $exists: false } },
          { is_deleted: { $exists: true, $eq: false } },
        ],
        book_recomm: true,
        collection_type,
      },
    },
    {
      $group: {
        _id: {
          book_id: "$_id",
          title: "$title",
          isbn_no: "$isbn_no",
          author: "$author",
          available_quantity: "$available_quantity",
          stock_quantity: "$stock_quantity",
          added_by: "$added_by",
          imprints: "$imprints",
          publishingGroup: "$publishingGroup",
          category_id: "$category_id",
          book_recomm: "$book_recomm",
          book_recomm_datetime: "$book_recomm_datetime",
        },
        count: {
          $sum: 1,
        },
      },
    },
    {
      $group: {
        _id: null,
        data: {
          $push: {
            book_id: "$_id.book_id",
            book_recomm: "$_id.book_recomm",
            book_recomm_datetime: "$_id.book_recomm_datetime",
            title: "$_id.title",
            isbn_no: "$_id.isbn_no",
            author: "$_id.author",
            stock_quantity: "$_id.stock_quantity",
            available_quantity: "$_id.available_quantity",
            category_id: "$_id.category_id",
            added_by: "$_id.added_by",
            publishingGroup: "$_id.publishingGroup",
            imprints: "$_id.imprints",
            count: "$count",
          },
        },
      },
    },
    {
      $project: {
        data: 1,
      },
    },
    {
      $sort: {
        "data.book_recomm_datetime": -1,
      },
    },
  ]);

  if (result && result.length > 0) {
    sortedBooks = [...result[0].data, ...sortedBooks];
  }

  let booksArr = [];
  if (sortedBooks.length > 0) {
    sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
    let resx = await Admin.findOne({ email: email });
    for (let sortedBook of sortedBooks) {
      let books = {
        title: sortedBook.title,
        isbn_no: sortedBook.isbn_no,
        author: sortedBook.author,
        stock_quantity: sortedBook.stock_quantity,
        available_quantity: sortedBook.available_quantity,
        added_by: sortedBook.added_by,
        category_id: sortedBook.category_id,
        book_recomm: sortedBook.book_recomm,
        publishingGroup: sortedBook.publishingGroup,
        imprints: sortedBook.imprints,
        _id: sortedBook.book_id,
      };
      let book_count = sortedBook.count;

      var category_ids = books.category_id;
      var categoryList = [];
      await category_ids.map(async (el) => {
        if (catArray.length > 0) {
          await catArray.map(async (allCats) => {
            if (typeof allCats[el.toString()] != "undefined") {
              categoryList.push(allCats[el.toString()]);
            }
          });
        }
      });

      if (
        resx &&
        resx.role == "3" &&
        books.added_by.toString() == resx._id.toString()
      ) {
        booksArr.push({
          books: books,
          category: categoryList,
          counts: book_count,
        });
      } else if (resx && (resx.role == "1" || resx.role == "2")) {
        booksArr.push({
          books: books,
          category: categoryList,
          counts: book_count,
        });
      }
    }
    return booksArr;
  } else {
    return booksArr;
  }
}

/** 18-01-2022 -> Optimised **/
async function AllBooksbydate(book_id, startDate, endDate, all, email, collection_type) {
  let catArray = await findCats();
  var sortedBooks = [];
  var result = await Book.aggregate([{
    $match: {
      _id: {
        $ne: book_id
      },
      $or: [{ is_deleted: { $exists: false } }, { is_deleted: { $exists: true, $eq: false } }],
      added_at: {
        "$lte": new Date(endDate)
      },
      collection_type
    }
  },
  {
    $group: {
      "_id": {
        "book_id": "$_id",
        "title": "$title",
        "isbn_no": "$isbn_no",
        "author": "$author",
        "available_quantity": "$available_quantity",
        "stock_quantity": "$stock_quantity",
        "added_by": "$added_by",
        "imprints": "$imprints",
        "publishingGroup": "$publishingGroup",
        "category_id": "$category_id",
        "book_recomm": "$book_recomm"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $group: {
      "_id": null,
      "data": {
        "$push": {
          "book_id": "$_id.book_id",
          "title": "$_id.title",
          "isbn_no": "$_id.isbn_no",
          "author": "$_id.author",
          "stock_quantity": "$_id.stock_quantity",
          "available_quantity": "$_id.available_quantity",
          "category_id": "$_id.category_id",
          "added_by": "$_id.added_by",
          "publishingGroup": "$_id.publishingGroup",
          "imprints": "$_id.imprints",
          "count": "$count",
        }
      }
    }
  },
  {
    $project: {
      data: 1
    }
  },
  {
    $sort: {
      'data.book_id': -1
    }
  }
  ]);

  if (result && result.length > 0) {
    sortedBooks = [...result[0].data, ...sortedBooks];
  }

  let booksArr = [];
  if (sortedBooks.length > 0) {
    sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
    let resx = await Admin.findOne({ email: email });
    for (let sortedBook of sortedBooks) {
      let books = {
        title: sortedBook.title,
        isbn_no: sortedBook.isbn_no,
        author: sortedBook.author,
        stock_quantity: sortedBook.stock_quantity,
        available_quantity: sortedBook.available_quantity,
        added_by: sortedBook.added_by,
        publishingGroup: sortedBook.publishingGroup,
        imprints: sortedBook.imprints,
        category_id: sortedBook.category_id,
        _id: sortedBook.book_id
      };
      let book_count = sortedBook.count;

      var category_ids = books.category_id;
      var categoryList = [];
      await category_ids.map(async (el) => {
        if (catArray.length > 0) {
          await catArray.map(async (allCats) => {
            if (typeof allCats[el.toString()] != 'undefined') {
              categoryList.push(allCats[el.toString()]);
            }
          });
        }
      });

      if (resx.role == '3' && ((books.added_by).toString()) == ((resx._id).toString())) {
        booksArr.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      } else if (resx && (resx.role == '1' || resx.role == '2')) {
        booksArr.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      }
    }
    return booksArr;
  } else {
    return booksArr;
  }
}

async function findCats() {
  let catArray = [];
  await Category.find().then(async (catRes) => {
    if (catRes && catRes != null) {
      catRes.map(async (catR) => {
        let obj = [];
        obj[(catR._id).toString()] = catR.name;
        catArray.push(obj);
      });
    }
  });
  return catArray;
}

async function renewBooksbydateV3(startDate, endDate, email, collection_type) {
  //  const _startDate = moment(startDate)
  //    .tz("Asia/Hong_Kong")
  //    .startOf("d")
  //    .toDate();
  //  const _endDate = moment(endDate).tz("Asia/Hong_Kong").endOf("d").toDate();

  const admin = await Admin.findOne({
    email,
  });

  const condition = []
	const borrowMatchCondition = {
		$or: [
			{
				issue_date: {
					$gte: startDate,
					$lte: endDate,
				}
			},
			{
				reborrow_one_date: {
					$gte: startDate,
					$lte: endDate,
				}
			},
			{
				reborrow_two_date: {
					$gte: startDate,
					$lte: endDate,
				}
			}
		]
	}

	condition.push({
		$match: borrowMatchCondition
	}, {
		$project: {
			countWithRenewedCopies: {
				$switch: {
					branches: [
						{
							case: {
								$and: [
									{ $eq: ["$reborrowed_once", false] },
									{ $eq: ["$reborrowed_twice", false] }
								]
							},
							then: 1

						},
             {
							case: {
								$and: [
									{ $eq: ["$reborrowed_once", true] },
									{ $eq: ["$reborrowed_twice", false] }
								]
							},
							then: 2

						}, {
							case: {
								$and: [
									{ $eq: ["$reborrowed_once", true] },
									{ $eq: ["$reborrowed_twice", true] }
								]
							},
							then: 3
						}
					],
					default: 1
				}
			},
			user_id: "$user_id",
			book_id: "$book_id",
		}
	})

  // if (admin.role !== "1" && admin.role !== "2") {
  //   Object.assign(condition, {
  //     "books.added_by": admin._id,
  //   });
  // }

  // 根据bookId分组
	const conditionGroupByBookId = [].concat(condition, {
		$group: {
			"_id": {
				"book_id": "$book_id"
			},
			countWithRenewedCopies: {
				$sum: "$countWithRenewedCopies"
			},
			countWithoutRenewedCopies: {
				$sum: 1
			}
		}
	}, {
		$project: {
			book_id: "$_id.book_id",
			countWithRenewedCopies: "$countWithRenewedCopies",
			countWithoutRenewedCopies: "$countWithoutRenewedCopies"
		}
	})
	const resultGroupByBookId = await Borrow.aggregate(conditionGroupByBookId)
  const bookIds = resultGroupByBookId.map(x => x.book_id)
  let bookCondition = {
    _id: {
			$in: bookIds
		},
  }
  if (admin.role !== "1" && admin.role !== "2") {
    Object.assign(bookCondition, {
      "books.added_by": admin._id,
    });
  }
  // console.log("condition:", bookCondition);
	const allBooks = await Book.find(bookCondition);
  // console.log("books",allBooks);



  const categories = await Category.find({});
  //console.log(allBooks[0]);

  let items = resultGroupByBookId.map(x => {
    // console.log(x);

		const book = allBooks.find(y => y._id.toString() === x.book_id.toString())
    //console.log("book:", book);
    if (collection_type && book.collection_type !== collection_type) return null
    
    const category = book.category_id.map((y) => {
      const category = categories.find(
        (category) => category._id.toString() === y.toString()
      );
      return category ? category.name : " ";
    });
    
    const counts = x.countWithRenewedCopies - x.countWithoutRenewedCopies;

    return {
      books: book,
      category,
      counts,
    }
	})
  .filter(x => !!x)
  .sort((a, b) => b.counts - a.counts);

  /*
  let items = Object.keys(allBooks)
    .map((x) => {
      console.log(x);
      const books = allBooks.find((y) => y._id.toString() === x.toString());
      const category = books.category_id.map((y) => {
        const category = categories.find(
          (category) => category._id.toString() === y.toString()
        );
        return category ? category.name : " ";
      });
      const counts = R.sum(result[x].map((y) => y.count));
      return {
        books,
        category,
        counts,
      };
    })
    .sort((a, b) => b.counts - a.counts);
    */

  const otherBooks = await Book.aggregate([
    {
      $match: {
        $and: [
          {
            $or: [
              { is_deleted: { $exists: false } },
              { is_deleted: { $exists: true, $eq: false } },
            ],
          },
          {
            collection_type: collection_type,
            isbn_no: {
              $nin: (items || []).map(x => (x.books || {}).isbn_no).filter(x => !!x)
            }
          }
        ]
      }
    },
    {
      $lookup: {
        from: "categories",
        localField: "category_id",
        foreignField: "_id",
        as: "categories",
      },
    }
  ])

  items = items.concat(otherBooks.map(x => ({ books: x, category: x.categories.map(y => y.name || ''), counts: 0 })));

  return items

}

async function reservedBooksbydateV2(book_id, startDate, endDate, all, email, collection_type) {
  let catArray = await findCats();
  let sortedBooks = [];
  let result = await Reserve.aggregate([{
    $match: {
      "book_id": {
        $ne: book_id
      },
      reserve_date: {
        "$gte": new Date(startDate),
        "$lte": new Date(endDate)
      }
    }
  },
  {
    $lookup: {
      from: 'books',
      localField: 'book_id',
      foreignField: '_id',
      as: 'books'
    }
  },
  {
    $match: {
      "books.collection_type": collection_type
    }
  },
  {
    $unwind: "$books"
  },
  {
    $group: {
      "_id": {
        "book_id": "$book_id",
        "books": "$books"
      },
      count: {
        $sum: 1
      }
    }
  },
  {
    $group: {
      "_id": null,
      "data": {
        "$push": {
          "books": {
            "book_id": "$_id.books._id",
            "isbn_no": "$_id.books.isbn_no",
            "title": "$_id.books.title",
            "author": "$_id.books.author",
            "added_by": "$_id.books.added_by",
            "publishingGroup": "$_id.books.publishingGroup",
            "imprints": "$_id.books.imprints",
            "category_id": "$_id.books.category_id"
          },
          "book_id": "$_id.book_id",
          "count": "$count"
        }
      }
    }
  },
  {
    $project: {
      data: 1
    }
  },
  {
    $sort: {
      'data.book_id': -1
    }
  }
  ]);
  if (result && result.length > 0) {
    sortedBooks = [...result[0].data, ...sortedBooks];
  }
  let recommendedBooks = [];
  if (sortedBooks.length > 0) {
    sortedBooks = sortedBooks.sort((a, b) => b.count - a.count);
    let resx = await Admin.findOne({
      email: email
    });
    for (let sortedBook of sortedBooks) {
      let book_id = sortedBook.book_id;
      var books = sortedBook.books;
      var book_count = sortedBook.count;

      var category_ids = books.category_id;
      var categoryList = [];
      await category_ids.map(async (el) => {
        if (catArray.length > 0) {
          await catArray.map(async (allCats) => {
            if (typeof allCats[el.toString()] != 'undefined') {
              categoryList.push(allCats[el.toString()]);
            }
          });
        }
      });

      if (resx.role == '3' && ((books.added_by).toString()) == ((resx._id).toString())) {
        recommendedBooks.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      } else if (resx && (resx.role == '1' || resx.role == '2')) {
        recommendedBooks.push({
          books: books,
          category: categoryList,
          counts: book_count
        });
      }
    }
  }

  const otherBooks = await Book.aggregate([
    {
      $match: {
        $and: [
          {
            $or: [
              { is_deleted: { $exists: false } },
              { is_deleted: { $exists: true, $eq: false } },
            ],
          },
          {
            collection_type: collection_type,
            isbn_no: {
              $nin: (recommendedBooks || []).map(x => (x.books || {}).isbn_no).filter(x => !!x)
            }
          }
        ]
      }
    },
    {
      $lookup: {
        from: "categories",
        localField: "category_id",
        foreignField: "_id",
        as: "categories",
      },
    }
  ])

  const items = recommendedBooks.concat(otherBooks.map(x => ({ books: x, category: x.categories.map(y => y.name || ''), counts: 0 })));

  return items

  //return recommendedBooks;
}

async function recommendedBooksbydateV2(book_id, startDate, endDate, all, email, isPetronVisible, collection_type) {
  const borrowMatchCondition = {
    $or: [
      { issue_date: { $gte: startDate, $lte: endDate } },
      { reborrow_one_date: { $gte: startDate, $lte: endDate } },
      { reborrow_two_date: { $gte: startDate, $lte: endDate } }
    ]
  };

  const borrowRecords = await Borrow.find(borrowMatchCondition, {
    user_id: 1,
    book_id: 1,
    issue_date: 1,
    reborrowed_once: 1,
    reborrowed_twice: 1,
    reborrow_one_date: 1,
    reborrow_two_date: 1
  }).lean();

  const userBookStats = {};

  for (const record of borrowRecords) {
    const { book_id, user_id, issue_date, reborrow_one_date, reborrow_two_date, reborrowed_once, reborrowed_twice } = record;

    let count = 0;

    const inRange = (date) => date >= startDate && date <= endDate;

    if (!reborrowed_once && !reborrowed_twice) {
      if (inRange(issue_date)) count = 1;
    } else if (reborrowed_once && !reborrowed_twice) {
      const inIssue = inRange(issue_date);
      const inRe1 = inRange(reborrow_one_date);
      if (inIssue && inRe1) count = 2;
      else if (inIssue || inRe1) count = 1;
    } else if (reborrowed_once && reborrowed_twice) {
      const inIssue = inRange(issue_date);
      const inRe1 = inRange(reborrow_one_date);
      const inRe2 = inRange(reborrow_two_date);
      if (inIssue && inRe1 && inRe2) count = 3;
      else if (inIssue && inRe2 || inRe2 && inRe1) count = 2;
      else if (inIssue || inRe1 || inRe2) count = 1;
    }
    console.log("count:", count);
    console.log(record)
    if (count === 0) continue;

    const key = `${book_id}_${user_id}`;
    if (!userBookStats[key]) {
      userBookStats[key] = {
        book_id,
        user_id,
        countWithRenewedCopies: 0,
        countWithoutRenewedCopies: 0
      };
    }

    userBookStats[key].countWithRenewedCopies += count;
    userBookStats[key].countWithoutRenewedCopies += 1;
  }

  const groupedStats = Object.values(userBookStats);
  if (groupedStats.length === 0) return [];

  const bookIdSet = new Set(groupedStats.map(x => x.book_id.toString()));
  const books = await Book.find({ _id: { $in: Array.from(bookIdSet) } });
  const categories = await Category.find();

  const countSet = new Set();

  let items = groupedStats.map(stat => {
    const book = books.find(b => b._id.toString() === stat.book_id.toString());
    if (!book || (collection_type && book.collection_type !== collection_type)) return null;

    const matchedCategories = (book.category_id || []).map(catId => {
      const cat = categories.find(c => c._id.toString() === catId.toString());
      return cat ? cat.name : '';
    });

    countSet.add(stat.countWithRenewedCopies);

    const { added_by, author, category_id, imprints, isbn_no, publishingGroup, title } = book;
    return {
      books: { added_by, author, category_id, imprints, isbn_no, publishingGroup, title },
      category: matchedCategories,
      counts: stat.countWithRenewedCopies,
    };
  }).filter(Boolean).sort((a, b) => b.counts - a.counts);

  if (countSet.size <= 10) return items;

  const sortedCounts = Array.from(countSet).sort((a, b) => b - a);
  const minCount = sortedCounts[9];

  return items.filter(x => x.counts >= minCount);
}

module.exports = {
  isBookBorrowed,
  recommendedBooks,
  getAllBooksuploaded,
  isBookFav,
  isReBorrowAllowed,
  isBookInStock,
  updateAvailableStock,
  totalBookBorrowedInPresent,
  totalBookReservedInPresent,
  recommBooksbydate,
  recommendedBooksbydate,
  renewedBooksbydate,
  reservedBooksbydate,
  getunusedBookBydate,
  AllBooksbydate,
  previewBooksbydate,
  getunusedBookByDateV2,
  getunusedBookCountByDateV2,
  renewBooksbydateV2,
  renewBooksCountbydateV2,
  renewBooksbydateV3,
  reservedBooksbydateV2,
  recommendedBooksbydateV2,
};