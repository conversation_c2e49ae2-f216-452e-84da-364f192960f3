const mongoose = require('mongoose');
const Book = require('./Model/Book');
const Borrow = require('./Model/Borrow');
const Reading = require('./Model/Reading');
const BookHelper = require('./Helper/BookHelper');

// 连接数据库
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/jrc-service', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

async function testStockManagement() {
    console.log('=== 测试库存管理修复 ===\n');

    try {
        // 1. 找一本有库存的书进行测试
        const book = await Book.findOne({ 
            stock_quantity: { $gt: 0 },
            $or: [
                { is_deleted: { $exists: false } }, 
                { is_deleted: { $exists: true, $eq: false } }
            ]
        });

        if (!book) {
            console.log('没有找到可测试的书籍');
            return;
        }

        console.log(`测试书籍: ${book.title} (ID: ${book._id})`);
        console.log(`总库存: ${book.stock_quantity}, 当前可用: ${book.available_quantity}\n`);

        // 2. 检查当前占用情况
        const borrowCount = await Borrow.countDocuments({
            book_id: book._id,
            returned: false
        });

        const readingCount = await Reading.countDocuments({
            book_id: book._id,
            returned: false
        });

        console.log(`当前占用情况:`);
        console.log(`- 借阅中: ${borrowCount}`);
        console.log(`- 阅读中: ${readingCount}`);
        console.log(`- 总占用: ${borrowCount + readingCount}`);

        // 3. 计算理论可用库存
        const theoreticalAvailable = book.stock_quantity - borrowCount - readingCount;
        console.log(`- 理论可用库存: ${theoreticalAvailable}\n`);

        // 4. 使用 BookHelper.isBookInStock 检查库存
        console.log('=== 使用 BookHelper.isBookInStock 检查库存 ===');
        const helperStock = await BookHelper.isBookInStock(book._id);
        console.log(`BookHelper 返回的可用库存: ${helperStock}\n`);

        // 5. 检查数据库中的实际值
        const updatedBook = await Book.findById(book._id);
        console.log(`数据库中更新后的可用库存: ${updatedBook.available_quantity}\n`);

        // 6. 验证一致性
        if (helperStock === theoreticalAvailable && updatedBook.available_quantity === theoreticalAvailable) {
            console.log('✅ 库存计算一致，修复成功！');
        } else {
            console.log('❌ 库存计算不一致，需要进一步检查：');
            console.log(`  理论值: ${theoreticalAvailable}`);
            console.log(`  Helper返回: ${helperStock}`);
            console.log(`  数据库值: ${updatedBook.available_quantity}`);
        }

        // 7. 测试 updateAvailableStock 的 return 操作
        console.log('\n=== 测试 updateAvailableStock return 操作 ===');
        const beforeReturn = await BookHelper.isBookInStock(book._id);
        console.log(`归还前可用库存: ${beforeReturn}`);
        
        // 模拟归还操作
        await BookHelper.updateAvailableStock(book._id, 'return');
        
        const afterReturn = await BookHelper.isBookInStock(book._id);
        console.log(`归还后可用库存: ${afterReturn}`);
        
        // 验证归还后的库存是否正确
        const finalBorrowCount = await Borrow.countDocuments({
            book_id: book._id,
            returned: false
        });

        const finalReadingCount = await Reading.countDocuments({
            book_id: book._id,
            returned: false
        });

        const expectedAfterReturn = book.stock_quantity - finalBorrowCount - finalReadingCount;
        
        if (afterReturn === expectedAfterReturn) {
            console.log('✅ 归还操作库存计算正确！');
        } else {
            console.log('❌ 归还操作库存计算有误：');
            console.log(`  期望值: ${expectedAfterReturn}`);
            console.log(`  实际值: ${afterReturn}`);
        }

    } catch (error) {
        console.error('测试过程中出现错误:', error);
    } finally {
        mongoose.connection.close();
    }
}

// 运行测试
testStockManagement();
